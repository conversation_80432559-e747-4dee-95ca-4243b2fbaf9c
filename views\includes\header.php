<?php
// Include the facebook image helper
require_once APPROOT . '/helpers/facebook_image_helper.php';

// Include the facebook crawler helper
require_once APPROOT . '/helpers/facebook_crawler_helper.php';

// Handle Facebook crawler requests early
if (isFacebookCrawler()) {
    // Determine page type based on current URL
    $currentUrl = $_SERVER['REQUEST_URI'] ?? '';
    $pageType = 'home';

    if (strpos($currentUrl, '/event/') !== false) {
        $pageType = 'event';
    } elseif (strpos($currentUrl, '/show/') !== false) {
        $pageType = 'show';
    } elseif (strpos($currentUrl, '/gallery') !== false || strpos($currentUrl, '/image_editor') !== false) {
        $pageType = 'gallery';
    } elseif (strpos($currentUrl, '/about') !== false) {
        $pageType = 'about';
    }

    // Get page data if available
    $pageData = [];
    if (isset($data)) {
        $pageData = $data;
    }

    handleFacebookCrawler($pageType, $pageData);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- CSRF Token for JavaScript -->
    <meta name="csrf-token" content="<?php echo isset($_SESSION[CSRF_TOKEN_NAME]) ? $_SESSION[CSRF_TOKEN_NAME] : ''; ?>">
    <meta name="csrf-token-name" content="<?php echo CSRF_TOKEN_NAME; ?>">
    
    <?php if (isset($_SESSION['cache_buster'])): ?>
    <!-- Cache control headers to prevent caching after updates -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <?php endif; ?>
    <title><?php echo isset($pageTitle) ? $pageTitle : (isset($title) ? $title . ' - ' . APP_NAME : APP_NAME); ?></title>
    
    <!-- SEO Meta Tags -->
    <?php if (isset($pageDescription)): ?>
    <meta name="description" content="<?php echo $pageDescription; ?>">
    <?php endif; ?>
    <?php if (isset($pageUrl)): ?>
    <link rel="canonical" href="<?php echo $pageUrl; ?>">
    <?php endif; ?>

    <!-- Open Graph Meta Tags for Facebook Sharing -->
    <meta property="fb:app_id" content="<?php echo defined('FB_APP_ID') ? FB_APP_ID : ''; ?>">
    <meta property="og:site_name" content="<?php echo htmlspecialchars(APP_NAME); ?>">
    <meta property="og:title" content="<?php echo htmlspecialchars(isset($pageTitle) ? $pageTitle : (isset($title) ? $title : APP_NAME)); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars(isset($pageDescription) ? $pageDescription : 'Rowan Elite Rides - Public Events and Shows for Car Enthusiasts'); ?>">
    <meta property="og:url" content="<?php echo htmlspecialchars(isset($pageUrl) ? $pageUrl : (isset($_SERVER['REQUEST_URI']) ? BASE_URL . $_SERVER['REQUEST_URI'] : BASE_URL)); ?>">
    <meta property="og:type" content="<?php echo isset($pageType) ? $pageType : 'website'; ?>">
    <meta property="og:image" content="<?php echo htmlspecialchars(isset($pageImage) ? $pageImage : BASE_URL . '/public/images/logo.png'); ?>">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars(isset($pageTitle) ? $pageTitle : (isset($title) ? $title : APP_NAME)); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars(isset($pageDescription) ? $pageDescription : 'Rowan Elite Rides - Public Events and Shows for Car Enthusiasts'); ?>">
    <meta name="twitter:image" content="<?php echo htmlspecialchars(isset($pageImage) ? $pageImage : BASE_URL . '/public/images/logo.png'); ?>">
    
    <!-- Social Media Meta Tags -->
    <?php if (isset($additionalMetaTags)): ?>
    <?php echo $additionalMetaTags; ?>
    <?php endif; ?>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/public/css/style.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/public/css/image-viewer.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/public/css/notifications.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/public/css/mobile-notifications-fix.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/public/css/racing-navigation.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/public/css/racing-header.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/public/css/apple-device-compatibility.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/public/css/pwa-features.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/public/css/sprint1-components.css">

    <!-- PWA Manifest -->
    <link rel="manifest" href="<?php echo BASE_URL; ?>/manifest.json">
    
    <!-- PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="RER Events">
    <meta name="application-name" content="RER Events">
    <meta name="msapplication-TileColor" content="#1338BE">
    <meta name="msapplication-config" content="<?php echo BASE_URL; ?>/public/browserconfig.xml">

    <!-- Enhanced Apple Device Support -->
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="apple-mobile-web-app-orientations" content="portrait-any">
    <meta name="format-detection" content="telephone=no">
    <meta name="format-detection" content="date=no">
    <meta name="format-detection" content="address=no">
    <meta name="format-detection" content="email=no">

    <!-- iOS Safari Specific -->
    <meta name="theme-color" content="#1338BE">
    <meta name="msapplication-navbutton-color" content="#1338BE">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">

    <!-- Prevent iOS zoom on form inputs -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    
    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="192x192" href="<?php echo BASE_URL; ?>/public/images/icons/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="128x128" href="<?php echo BASE_URL; ?>/public/images/icons/icon-128x128.png">
    <link rel="icon" type="image/png" sizes="72x72" href="<?php echo BASE_URL; ?>/public/images/icons/icon-72x72.png">
    <link rel="icon" type="image/png" sizes="96x96" href="<?php echo BASE_URL; ?>/public/images/icons/icon-96x96.png">
    <link rel="mask-icon" href="<?php echo BASE_URL; ?>/public/images/icons/safari-pinned-tab.svg" color="#1338BE">

    <?php
    // Include front-page CSS for home page
    $current_page = $_SERVER['REQUEST_URI'] ?? '';
    $is_home_page = ($current_page === '/' || $current_page === '' || strpos($current_page, '/home') !== false || strpos($current_page, 'index.php') !== false);
    if ($is_home_page):
    ?>
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/public/css/front-page.css">
    <?php endif; ?>
    
    <?php 
    // Get custom CSS from settings
    if (!isset($settingsModel)) {
        require_once APPROOT . '/models/SettingsModel.php';
        $settingsModel = new SettingsModel();
    }
    
    // Include page-specific custom CSS if provided
    if (isset($custom_css) && !empty($custom_css)) {
        echo '<link rel="stylesheet" href="' . $custom_css . '">';
    }
    
    // Get global custom CSS from settings
    $global_custom_css = $settingsModel->getSetting('custom_css', '');
    $primary_color = $settingsModel->getSetting('primary_color', '#007bff');
    $secondary_color = $settingsModel->getSetting('secondary_color', '#6c757d');
    $accent_color = $settingsModel->getSetting('accent_color', '#28a745');
    $background_color = $settingsModel->getSetting('background_color', '#f8f9fa');
    $text_color = $settingsModel->getSetting('text_color', '#212529');
    
    // Include global custom CSS if it exists
    if (!empty($global_custom_css)) {
        echo '<link rel="stylesheet" href="' . $global_custom_css . '">';
    }
    
    if (!empty($primary_color) || !empty($secondary_color) || !empty($accent_color) || !empty($background_color) || !empty($text_color)) : 
    ?>
    <style>
        :root {
            --primary-color: <?php echo $primary_color; ?>;
            --secondary-color: <?php echo $secondary_color; ?>;
            --accent-color: <?php echo $accent_color; ?>;
            --background-color: <?php echo $background_color; ?>;
            --text-color: <?php echo $text_color; ?>;
        }
        
        body {
            background-color: var(--background-color);
            color: var(--text-color);
        }
        
        .bg-primary {
            background-color: var(--primary-color) !important;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        .text-primary {
            color: var(--primary-color) !important;
        }
        
        .alert-success, .bg-success {
            background-color: var(--accent-color) !important;
        }
        
        /* Custom CSS from branding settings */
        <?php echo isset($custom_css) ? $custom_css : ''; ?>
    </style>
    <?php endif; ?>

    <!-- Header Background Image Support -->
    <?php
    // Get header background settings
    $header_bg_image = '';
    $header_bg_size = 'cover';
    $header_bg_position = 'center';
    $header_carbon_opacity = 60;
    $header_bg_brightness = 40;

    // Get logo sizing settings
    $logo_size_desktop = 60;
    $logo_size_mobile = 63;
    $logo_size_tablet = 62;
    $logo_size_apple = 63;

    if (isset($settingsModel)) {
        $header_bg_image = $settingsModel->getSetting('header_bg_image', '');
        $header_bg_size = $settingsModel->getSetting('header_bg_size', 'cover');
        $header_bg_position = $settingsModel->getSetting('header_bg_position', 'center');
        $header_carbon_opacity = intval($settingsModel->getSetting('header_carbon_opacity', '60'));
        $header_bg_brightness = intval($settingsModel->getSetting('header_bg_brightness', '40'));

        // Get logo sizing settings
        $logo_size_desktop = intval($settingsModel->getSetting('logo_size_desktop', '60'));
        $logo_size_mobile = intval($settingsModel->getSetting('logo_size_mobile', '63'));
        $logo_size_tablet = intval($settingsModel->getSetting('logo_size_tablet', '62'));
        $logo_size_apple = intval($settingsModel->getSetting('logo_size_apple', '63'));
    }
    if (!empty($header_bg_image)) :
        // Calculate carbon fiber overlay opacity
        $carbon_overlay_opacity = $header_carbon_opacity / 100 * 0.8; // Max 0.8 opacity for carbon fiber

        // Ensure opacity values stay within reasonable bounds
        $carbon_overlay_opacity = max(0, min(0.9, $carbon_overlay_opacity));
    ?>
    <style>
        .racing-header.has-bg-image {
            background:
                /* Carbon fiber weave overlay - separate control */
                linear-gradient(45deg, rgba(42, 42, 42, <?php echo $carbon_overlay_opacity; ?>) 25%, transparent 25%),
                linear-gradient(-45deg, rgba(42, 42, 42, <?php echo $carbon_overlay_opacity; ?>) 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, rgba(42, 42, 42, <?php echo $carbon_overlay_opacity; ?>) 75%),
                linear-gradient(-45deg, transparent 75%, rgba(42, 42, 42, <?php echo $carbon_overlay_opacity; ?>) 75%),
                /* Carbon fiber dots overlay */
                radial-gradient(circle at 25% 25%, rgba(68, 68, 68, <?php echo $carbon_overlay_opacity * 0.8; ?>) 1px, transparent 1px),
                radial-gradient(circle at 75% 75%, rgba(68, 68, 68, <?php echo $carbon_overlay_opacity * 0.8; ?>) 1px, transparent 1px),
                /* Brightness overlay for image dimming */
                linear-gradient(rgba(0, 0, 0, <?php echo (100 - $header_bg_brightness) / 100; ?>), rgba(0, 0, 0, <?php echo (100 - $header_bg_brightness) / 100; ?>)),
                /* User background image */
                url('<?php echo htmlspecialchars($header_bg_image); ?>'),
                /* Fallback gradient */
                linear-gradient(135deg, #1a1a1a, #2d2d2d, #1a1a1a) !important;
            background-size:
                12px 12px,
                12px 12px,
                12px 12px,
                12px 12px,
                6px 6px,
                6px 6px,
                cover,
                <?php echo htmlspecialchars($header_bg_size); ?>,
                100% 100% !important;
            background-position:
                0 0,
                0 0,
                0 0,
                0 0,
                0 0,
                3px 3px,
                <?php echo htmlspecialchars($header_bg_position); ?>,
                <?php echo htmlspecialchars($header_bg_position); ?>,
                0 0 !important;
            background-repeat:
                repeat,
                repeat,
                repeat,
                repeat,
                repeat,
                repeat,
                no-repeat,
                no-repeat,
                no-repeat !important;
        }
    </style>
    <?php endif; ?>

    <!-- Dynamic Logo Sizing -->
    <style>
        /* Bootstrap-proof logo sizing with high specificity */
        .racing-header .racing-brand img,
        .navbar .racing-brand img,
        a.racing-brand img {
            height: <?php echo $logo_size_desktop; ?>px !important;
            width: auto !important;
            max-height: <?php echo $logo_size_desktop; ?>px !important;
            min-height: auto !important;
            object-fit: contain !important;
            transition: all 0.3s ease !important;
        }

        /* Tablet logo size */
        @media (min-width: 768px) and (max-width: 991.98px) {
            .racing-header .racing-brand img,
            .navbar .racing-brand img,
            a.racing-brand img {
                height: <?php echo $logo_size_tablet; ?>px !important;
                max-height: <?php echo $logo_size_tablet; ?>px !important;
            }
        }

        /* Mobile logo size */
        @media (max-width: 767.98px) {
            .racing-header .racing-brand img,
            .navbar .racing-brand img,
            a.racing-brand img {
                height: <?php echo $logo_size_mobile; ?>px !important;
                max-height: <?php echo $logo_size_mobile; ?>px !important;
                min-height: <?php echo $logo_size_mobile; ?>px !important;
            }
        }

        /* Apple device logo size */
        .apple-device .racing-header .racing-brand img,
        .apple-device .navbar .racing-brand img,
        .apple-device a.racing-brand img {
            height: <?php echo $logo_size_apple; ?>px !important;
            max-height: <?php echo $logo_size_apple; ?>px !important;
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
        }

        /* Hide header and breadcrumb during PWA camera/QR scanner modals */
        body:has(.camera-modal),
        body:has(.qr-scanner-modal) {
            .racing-header,
            .breadcrumb-navigation,
            .mobile-role-bar {
                display: none !important;
            }
        }

        /* Fallback for browsers that don't support :has() */
        .camera-modal-active .racing-header,
        .camera-modal-active .breadcrumb-navigation,
        .camera-modal-active .mobile-role-bar,
        .qr-scanner-active .racing-header,
        .qr-scanner-active .breadcrumb-navigation,
        .qr-scanner-active .mobile-role-bar {
            display: none !important;
        }
    </style>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Apple Device Compatibility -->
    <script src="<?php echo BASE_URL; ?>/public/js/apple-device-compatibility.js"></script>

    <!-- Header Settings -->
    <script src="<?php echo BASE_URL; ?>/public/js/header-settings.js"></script>

    <!-- Firebase SDK v9.23.0 (stable version with better pushManager support) -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-messaging-compat.js"></script>

    <!-- FCM Notifications -->
    <script src="<?php echo BASE_URL; ?>/public/js/fcm-notifications.js"></script>

    <!-- Notification Center -->
    <script src="<?php echo BASE_URL; ?>/public/js/notification-center.js?v=<?php echo time(); ?>"></script>
    
    <!-- Notification Permission Manager -->
    <script src="<?php echo BASE_URL; ?>/public/js/notification-permission-manager.js"></script>

    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?php echo isset($csrf_token) ? htmlspecialchars($csrf_token) : ''; ?>">
    
    <!-- Base URL for JavaScript -->
    <script>
        const BASE_URL = '<?php echo BASE_URL; ?>';
        
        // PWA Configuration for FCM
        window.PWA_CONFIG = {
            userId: <?php echo isset($_SESSION['user_id']) ? (int)$_SESSION['user_id'] : 'null'; ?>,
            username: <?php echo isset($_SESSION['username']) ? json_encode($_SESSION['username']) : 'null'; ?>,
            isLoggedIn: <?php echo isset($_SESSION['user_id']) ? 'true' : 'false'; ?>
        };
        
        <?php if (isset($_SESSION['cache_buster'])): ?>
        // Cache buster for JavaScript files
        const CACHE_BUSTER = '<?php echo $_SESSION['cache_buster']; ?>';
        
        // Function to add cache buster to script and link tags
        document.addEventListener('DOMContentLoaded', function() {
            // Add cache buster to script tags
            document.querySelectorAll('script[src]').forEach(function(script) {
                if (script.src.indexOf('?') === -1) {
                    script.src = script.src + '?v=' + CACHE_BUSTER;
                } else {
                    script.src = script.src + '&v=' + CACHE_BUSTER;
                }
            });
            
            // Add cache buster to link tags (CSS)
            document.querySelectorAll('link[rel="stylesheet"]').forEach(function(link) {
                if (link.href.indexOf('?') === -1) {
                    link.href = link.href + '?v=' + CACHE_BUSTER;
                } else {
                    link.href = link.href + '&v=' + CACHE_BUSTER;
                }
            });
        });
        <?php endif; ?>
    </script>
    
    <?php 
    // Get favicon from settings
    if (!isset($settingsModel)) {
        require_once APPROOT . '/models/SettingsModel.php';
        $settingsModel = new SettingsModel();
    }
    $site_favicon = $settingsModel->getSetting('site_favicon', '');
    
    if (!empty($site_favicon)) : 
    ?>
    <!-- Favicon -->
    <link rel="icon" href="<?php echo BASE_URL . $site_favicon; ?>" type="image/x-icon">
    <link rel="shortcut icon" href="<?php echo BASE_URL . $site_favicon; ?>" type="image/x-icon">
    <?php endif; ?>
</head>
<body data-debug-mode="<?php echo defined('DEBUG_MODE') && DEBUG_MODE ? 'true' : 'false'; ?>" 
      data-user-id="<?php echo isset($_SESSION['user_id']) ? $_SESSION['user_id'] : ''; ?>">
    <!-- Racing Navigation -->
    <nav class="racing-header <?php echo !empty($header_bg_image) ? 'has-bg-image' : ''; ?>">
        <div class="container">
            <div class="navbar racing-navbar navbar-expand-md">
                <!-- Racing Brand/Logo -->
                <a class="racing-brand d-flex align-items-center" href="<?php echo BASE_URL; ?>">
                    <?php
                    // Get site logo from settings
                    if (!isset($settingsModel)) {
                        require_once APPROOT . '/models/SettingsModel.php';
                        $settingsModel = new SettingsModel();
                    }
                    $site_logo = $settingsModel->getSetting('site_logo', '');

                    if (!empty($site_logo)) :
                    ?>
                        <img src="<?php echo BASE_URL . $site_logo; ?>" alt="<?php echo APP_NAME; ?>" height="60" class="d-inline-block align-text-top">
                    <?php else : ?>
                        <?php echo APP_NAME; ?>
                    <?php endif; ?>
                </a>



                <!-- Mobile Menu Button (Racing Style) -->
                <button class="racing-menu-btn d-lg-none" type="button" onclick="toggleRacingDrawer();">
                    <span class="racing-menu-text">MENU</span>
                </button>

                <!-- Desktop Navigation Menu -->
                <div class="collapse navbar-collapse d-none d-lg-flex" id="navbarNav">
                    <!-- Main Navigation -->
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item dropdown">
                            <a class="racing-nav-link dropdown-toggle" href="#" id="eventsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-calendar-alt me-1"></i>Find Events
                            </a>
                            <ul class="racing-dropdown-menu dropdown-menu" aria-labelledby="eventsDropdown">
                                <li><h6 class="dropdown-header"><i class="fas fa-search me-2"></i>Find Car Shows</h6></li>
                                <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/calendar">
                                    <i class="fas fa-calendar me-2"></i>Event Calendar
                                    <small class="d-block text-muted">Browse all upcoming shows</small>
                                </a></li>
                                <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/show">
                                    <i class="fas fa-trophy me-2"></i>Car Shows
                                    <small class="d-block text-muted">View show details & register</small>
                                </a></li>
                                <li><hr class="racing-divider dropdown-divider"></li>
                                <li><h6 class="dropdown-header"><i class="fas fa-plus me-2"></i>Host Your Own</h6></li>
                                <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/calendar/createEvent">
                                    <i class="fas fa-plus-circle me-2"></i>Add Community Event
                                    <small class="d-block text-muted">List your local car event</small>
                                </a></li>
                                <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/user/createShow">
                                    <i class="fas fa-crown me-2"></i>Create Car Show
                                    <small class="d-block text-muted">Host with judging & awards</small>
                                </a></li>
                                <li><hr class="racing-divider dropdown-divider"></li>
                                <li><h6 class="dropdown-header"><i class="fas fa-question-circle me-2"></i>Need Help?</h6></li>
                                <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/home/<USER>">
                                    <i class="fas fa-play-circle me-2"></i>How It Works
                                    <small class="d-block text-muted">Interactive site tour</small>
                                </a></li>
                                <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/home/<USER>">
                                    <i class="fas fa-book me-2"></i>Host Guide
                                    <small class="d-block text-muted">Complete coordinator guide</small>
                                </a></li>
                            </ul>
                        </li>
                        <?php if (isset($_SESSION['user_id'])) : ?>
                            <li class="nav-item dropdown">
                                <a class="racing-nav-link dropdown-toggle" href="#" id="dashboardDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                                </a>
                                <ul class="racing-dropdown-menu dropdown-menu" aria-labelledby="dashboardDropdown">
                                    <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/user/dashboard">
                                        <i class="fas fa-home me-2"></i>My Dashboard
                                    </a></li>
                                    <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin') : ?>
                                        <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/admin/dashboard">
                                            <i class="fas fa-crown me-2"></i>Admin Dashboard
                                        </a></li>
                                    <?php endif; ?>
                                    <?php if (isset($_SESSION['user_role']) && in_array($_SESSION['user_role'], ['admin', 'coordinator'])) : ?>
                                        <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/dashboard">
                                            <i class="fas fa-clipboard-list me-2"></i>Coordinator Dashboard
                                        </a></li>
                                    <?php endif; ?>
                                    <?php if (isset($_SESSION['user_role']) && in_array($_SESSION['user_role'], ['admin', 'judge'])) : ?>
                                        <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/judge/dashboard">
                                            <i class="fas fa-gavel me-2"></i>Judge Dashboard
                                        </a></li>
                                    <?php endif; ?>
                                    <?php if (isset($_SESSION['user_role']) && in_array($_SESSION['user_role'], ['admin', 'staff'])) : ?>
                                        <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/staff/dashboard">
                                            <i class="fas fa-users me-2"></i>Staff Dashboard
                                        </a></li>
                                    <?php endif; ?>
                                </ul>
                            </li>
                            <!-- Help & Support Menu for all logged-in users -->
                            <li class="nav-item dropdown">
                                <a class="racing-nav-link dropdown-toggle" href="#" id="helpDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-question-circle me-1"></i>Help
                                </a>
                                <ul class="racing-dropdown-menu dropdown-menu" aria-labelledby="helpDropdown">
                                    <li><h6 class="dropdown-header"><i class="fas fa-book me-2"></i>Guides & Tutorials</h6></li>
                                    <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/home/<USER>">
                                        <i class="fas fa-play-circle me-2"></i>Getting Started
                                        <small class="d-block text-muted">Complete beginner guide</small>
                                    </a></li>
                                    <?php if (isset($_SESSION['user_role']) && in_array($_SESSION['user_role'], ['admin', 'judge'])) : ?>
                                    <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/judge/tutorial">
                                        <i class="fas fa-graduation-cap me-2"></i>Judging Tutorial
                                        <small class="d-block text-muted">Step-by-step judging guide</small>
                                    </a></li>
                                    <?php endif; ?>
                                    <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/home/<USER>">
                                        <i class="fas fa-crown me-2"></i>Host Guide
                                        <small class="d-block text-muted">Become a coordinator</small>
                                    </a></li>
                                    <li><hr class="racing-divider dropdown-divider"></li>
                                    <li><h6 class="dropdown-header"><i class="fas fa-tools me-2"></i>Your Tools</h6></li>
                                    <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/judge_info">
                                        <i class="fas fa-gavel me-2"></i>Your Judges
                                    </a></li>
                                    <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/judging_conflict/my_reports">
                                        <i class="fas fa-flag me-2"></i>My Reports
                                    </a></li>
                                    <li><hr class="racing-divider dropdown-divider"></li>
                                    <li><h6 class="dropdown-header"><i class="fas fa-support me-2"></i>Support</h6></li>
                                    <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/judging_conflict/report">
                                        <i class="fas fa-plus me-2"></i>Report Issue
                                    </a></li>
                                    <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/help/voting_security">
                                        <i class="fas fa-shield-alt me-2"></i>Voting Security
                                        <small class="d-block text-muted">Fan voting help & appeals</small>
                                    </a></li>
                                    <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/user/help">
                                        <i class="fas fa-life-ring me-2"></i>Help Center
                                    </a></li>
                                </ul>
                            </li>
                        <?php endif; ?>
                        <?php if (isset($_SESSION['user_role']) && in_array($_SESSION['user_role'], ['admin', 'coordinator', 'judge', 'staff'])) : ?>
                            <li class="nav-item dropdown">
                                <a class="racing-nav-link dropdown-toggle" href="#" id="managementDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-cogs me-1"></i>Management
                                </a>
                                <ul class="racing-dropdown-menu dropdown-menu" aria-labelledby="managementDropdown">
                                    <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin') : ?>
                                        <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/admin/users">
                                            <i class="fas fa-users me-2"></i>User Management
                                        </a></li>
                                        <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/show_roles/myRequests">
                                            <i class="fas fa-users-cog me-2"></i>Show Role Manager
                                        </a></li>
                                    <?php endif; ?>
                                    <?php if (isset($_SESSION['user_role']) && in_array($_SESSION['user_role'], ['admin', 'coordinator'])) : ?>
                                        <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/admin/shows">
                                            <i class="fas fa-calendar-check me-2"></i>Show Management
                                        </a></li>
                                        <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/judging_conflict/dashboard">
                                            <i class="fas fa-exclamation-triangle me-2"></i>Judging Conflicts
                                        </a></li>
                                    <?php endif; ?>
                                    <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin') : ?>
                                        <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/admin/registerVehicle">
                                            <i class="fas fa-car me-2"></i>Register Vehicle for User
                                        </a></li>
                                        <li><hr class="racing-divider dropdown-divider"></li>
                                        <li><a class="racing-dropdown-item dropdown-item" href="<?php echo BASE_URL; ?>/admin/settings">
                                            <i class="fas fa-cog me-2"></i>Admin Settings
                                        </a></li>
                                    <?php endif; ?>
                                </ul>
                            </li>
                        <?php endif; ?>
                    </ul>

                    <!-- Right Side Navigation -->
                    <ul class="navbar-nav">
                        <?php if (isset($_SESSION['user_id'])) : ?>
                            <!-- Notifications -->
                            <li class="nav-item">
                                <a class="racing-notification nav-link position-relative px-3" href="<?php echo BASE_URL; ?>/notification_center">
                                <i class="fas fa-bell"></i>
                                <?php
                                // Get unread notification count from notification center
                                $unread_count = 0;
                                if (isset($_SESSION['user_id'])) {
                                    try {
                                        require_once APPROOT . '/models/UnifiedMessageModel.php';
                                        $unifiedMessageModel = new UnifiedMessageModel();
                                        $headerMessages = $unifiedMessageModel->getUserMessages($_SESSION['user_id'], 'all');
                                        
                                        // Manual count for unread messages
                                        $unread_count = 0;
                                        foreach ($headerMessages as $headerMessage) {
                                            if ($headerMessage->is_read == 0 && $headerMessage->is_archived == 0) {
                                                $unread_count++;
                                            }
                                        }
                                    } catch (Exception $e) {
                                        // Silently fail if notification system has issues
                                        $unread_count = 0;
                                    }
                                }
                                if ($unread_count > 0) :
                                ?>
                                <span class="notification-count-simple"
                                      data-count="<?php echo $unread_count; ?>"
                                      id="notification-count"
                                      style="position: absolute;
                                             top: 2px;
                                             right: -5px;
                                             background: none !important;
                                             color: #FFD700 !important;
                                             font-weight: bold !important;
                                             font-size: 12px !important;
                                             text-shadow: 1px 1px 2px rgba(0,0,0,0.8) !important;
                                             z-index: 1000 !important;
                                             line-height: 1 !important;">
                                    <?php echo $unread_count > 99 ? '99+' : $unread_count; ?>
                                </span>
                                <?php endif; ?>
                            </a>
                        </li>



                            <!-- User Account Dropdown -->
                            <li class="nav-item dropdown">
                                <a class="racing-nav-link dropdown-toggle d-flex align-items-center px-3" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <?php
                                    // Use our helper function to display the profile image
                                    $imageUrl = getUserProfileImageUrl($_SESSION['user_id']);
                                    if ($imageUrl) :
                                    ?>
                                        <div class="racing-user-avatar rounded-circle overflow-hidden me-2" style="width: 32px; height: 32px;">
                                            <img src="<?php echo htmlspecialchars($imageUrl); ?>" alt="Profile" class="img-fluid" style="object-fit: cover; width: 100%; height: 100%;">
                                        </div>
                                    <?php else : ?>
                                        <div class="racing-user-avatar rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px; background-color: rgba(192,192,192,0.2);">
                                            <i class="fas fa-user" style="color: #c0c0c0;"></i>
                                        </div>
                                    <?php endif; ?>
                                    <span class="d-none d-lg-inline">
                                        <?php
                                        if (isset($_SESSION['user_name'])) {
                                            echo htmlspecialchars($_SESSION['user_name']);
                                        } else {
                                            echo 'Account';
                                        }
                                        ?>
                                    </span>
                                    <span class="d-lg-none">Account</span>
                                </a>
                                <ul class="racing-dropdown-menu dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown" style="min-width: 220px;">
                                    <li><a class="racing-dropdown-item dropdown-item py-2" href="<?php echo BASE_URL; ?>/user/dashboard">
                                        <i class="fas fa-home me-2"></i>My Dashboard
                                    </a></li>
                                    <li><a class="racing-dropdown-item dropdown-item py-2" href="<?php echo BASE_URL; ?>/user/profile">
                                        <i class="fas fa-user-edit me-2"></i>My Profile
                                    </a></li>
                                    <li><a class="racing-dropdown-item dropdown-item py-2" href="<?php echo BASE_URL; ?>/user/vehicles">
                                        <i class="fas fa-car me-2"></i>My Vehicles
                                    </a></li>
                                    <li><a class="racing-dropdown-item dropdown-item py-2" href="<?php echo BASE_URL; ?>/user/registrations">
                                        <i class="fas fa-ticket-alt me-2"></i>My Registrations
                                    </a></li>
                                    <li><a class="racing-dropdown-item dropdown-item py-2" href="<?php echo BASE_URL; ?>/notification_center">
                                        <i class="fas fa-bell me-2"></i>Messages
                                    </a></li>
                                    <li><a class="racing-dropdown-item dropdown-item py-2" href="<?php echo BASE_URL; ?>/judging_conflict/my_reports">
                                        <i class="fas fa-flag me-2"></i>My Conflict Reports
                                    </a></li>
                                    <li><hr class="racing-divider dropdown-divider"></li>
                                    <li><a class="racing-dropdown-item dropdown-item py-2" href="<?php echo BASE_URL; ?>/auth/logout" style="color: #ff6b6b !important;">
                                        <i class="fas fa-sign-out-alt me-2"></i>Sign Out
                                    </a></li>
                                </ul>
                            </li>
                        <?php else : ?>
                            <!-- Help for Non-Users -->
                            <li class="nav-item">
                                <a class="racing-nav-link" href="<?php echo BASE_URL; ?>/home/<USER>">
                                    <i class="fas fa-question-circle me-1"></i>Help
                                </a>
                            </li>
                            <!-- Login/Register for Non-Users -->
                            <li class="nav-item">
                                <a class="racing-login-btn nav-link px-3" href="<?php echo BASE_URL; ?>/auth/login">
                                    <i class="fas fa-sign-in-alt me-1"></i>Login
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="racing-register-btn nav-link px-3 btn btn-sm ms-2" href="<?php echo BASE_URL; ?>/auth/register">
                                    <i class="fas fa-user-plus me-1"></i>Get Started Free
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Racing Drawer -->
    <div id="racingDrawer" class="racing-drawer">
        <div class="racing-drawer-content">
            <div class="racing-drawer-header">
                <button class="racing-drawer-close" onclick="closeRacingDrawer()" aria-label="Close Menu">×</button>
                <div class="chrome-line"></div>
                <h3 class="racing-title">NAVIGATION</h3>
                <div class="chrome-line"></div>
            </div>

            <div class="racing-buttons-grid">
                <!-- Public Section -->
                <div class="racing-section-header">
                    <i class="fas fa-globe"></i> Public Access
                </div>
                <div class="racing-button-group">
                    <!-- Calendar Button -->
                    <div class="racing-button" onclick="toggleSubmenu('calendar')">
                        <div class="button-chrome-bezel">
                            <div class="button-content">
                                <i class="fas fa-calendar-alt"></i>
                                <span>CALENDAR</span>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Management Section -->
                <?php if (isset($_SESSION['user_role']) && in_array($_SESSION['user_role'], ['admin', 'coordinator', 'judge', 'staff'])) : ?>
                <div class="racing-section-header">
                    <i class="fas fa-crown"></i> Management
                </div>
                <div class="racing-button-group">
                    <div class="racing-button" onclick="toggleSubmenu('management')">
                        <div class="button-chrome-bezel">
                            <div class="button-content">
                                <i class="fas fa-cogs"></i>
                                <span>MANAGEMENT</span>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Account Section -->
                <?php if (isset($_SESSION['user_id'])) : ?>
                <div class="racing-section-header">
                    <i class="fas fa-user-circle"></i> Account
                </div>
                <div class="racing-button-group">
                    <!-- Logout Button -->
                    <div class="racing-button logout-button" onclick="window.location.href='<?php echo BASE_URL; ?>/auth/logout'">
                        <div class="button-chrome-bezel logout-bezel">
                            <div class="button-content">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>LOGOUT</span>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="racing-section-header">
                    <i class="fas fa-sign-in-alt"></i> Get Started
                </div>
                <div class="racing-button-group">
                    <!-- Login Button -->
                    <div class="racing-button" onclick="window.location.href='<?php echo BASE_URL; ?>/auth/login'">
                        <div class="button-chrome-bezel">
                            <div class="button-content">
                                <i class="fas fa-sign-in-alt"></i>
                                <span>LOGIN</span>
                            </div>
                        </div>
                    </div>

                    <!-- Register Button -->
                    <div class="racing-button" onclick="window.location.href='<?php echo BASE_URL; ?>/auth/register'">
                        <div class="button-chrome-bezel">
                            <div class="button-content">
                                <i class="fas fa-user-plus"></i>
                                <span>REGISTER</span>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Submenus -->
            <div id="submenu-calendar" class="racing-submenu">
                <a href="<?php echo BASE_URL; ?>/calendar" class="racing-submenu-item">
                    <i class="fas fa-calendar"></i> Calendar
                </a>
                <a href="<?php echo BASE_URL; ?>/show" class="racing-submenu-item">
                    <i class="fas fa-trophy"></i> Shows
                </a>
                <a href="<?php echo BASE_URL; ?>/calendar/createEvent" class="racing-submenu-item">
                    <i class="fas fa-plus-circle"></i> Add Event
                </a>
                <a href="<?php echo BASE_URL; ?>/user/createShow" class="racing-submenu-item">
                    <i class="fas fa-bullseye"></i> Create Show
                </a>
            </div>



            <?php if (isset($_SESSION['user_role']) && in_array($_SESSION['user_role'], ['admin', 'coordinator', 'judge', 'staff'])) : ?>
            <div id="submenu-management" class="racing-submenu">
                <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin') : ?>
                <a href="<?php echo BASE_URL; ?>/admin/users" class="racing-submenu-item">
                    <i class="fas fa-users"></i> User Management
                </a>
                <a href="<?php echo BASE_URL; ?>/show_roles/myRequests" class="racing-submenu-item">
                    <i class="fas fa-users-cog"></i> Show Role Manager
                </a>
                <?php endif; ?>
                <?php if (isset($_SESSION['user_role']) && in_array($_SESSION['user_role'], ['admin', 'coordinator'])) : ?>
                <a href="<?php echo BASE_URL; ?>/admin/shows" class="racing-submenu-item">
                    <i class="fas fa-calendar-check"></i> Show Management
                </a>
                <?php endif; ?>
                <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin') : ?>
                <a href="<?php echo BASE_URL; ?>/admin/registerVehicle" class="racing-submenu-item">
                    <i class="fas fa-car"></i> Register Vehicle for User
                </a>
                <a href="<?php echo BASE_URL; ?>/admin/settings" class="racing-submenu-item">
                    <i class="fas fa-cog"></i> Admin Settings
                </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Racing Drawer Overlay -->
    <div id="racingOverlay" class="racing-overlay" onclick="closeRacingDrawer()"></div>

    <!-- Inline Racing Navigation Script for Testing -->
    <script>
    // Toggle Racing Drawer
    function toggleRacingDrawer() {
        const drawer = document.getElementById('racingDrawer');
        const overlay = document.getElementById('racingOverlay');

        if (!drawer || !overlay) {
            return;
        }

        if (drawer.classList.contains('open')) {
            drawer.classList.remove('open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        } else {
            drawer.classList.add('open');
            overlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    function closeRacingDrawer() {
        const drawer = document.getElementById('racingDrawer');
        const overlay = document.getElementById('racingOverlay');

        if (drawer && overlay) {
            drawer.classList.remove('open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    function toggleSubmenu(menuId) {
        const submenu = document.getElementById('submenu-' + menuId);
        const allSubmenus = document.querySelectorAll('.racing-submenu');

        // Close all other submenus
        allSubmenus.forEach(menu => {
            if (menu.id !== 'submenu-' + menuId) {
                menu.classList.remove('active');
            }
        });

        // Toggle current submenu
        if (submenu) {
            submenu.classList.toggle('active');
        }
    }
    </script>
    
    <!-- Admin Impersonation Banner -->
    <?php if (isset($_SESSION['admin_impersonating']) && $_SESSION['admin_impersonating']): ?>
    <div class="alert alert-warning mb-0 text-center py-2" style="border-radius: 0;">
        <div class="container d-flex justify-content-between align-items-center">
            <span>
                <i class="fas fa-user-secret me-2"></i> You are currently viewing the site as 
                <?php 
                    // Get the current user's name from session or fallback
                    $userName = $_SESSION['user_name'] ?? 'Unknown User';
                    $userRole = $_SESSION['user_role'] ?? 'user';
                    echo '<strong>' . htmlspecialchars($userName) . '</strong> (' . ucfirst(htmlspecialchars($userRole)) . ')';
                ?>
            </span>
            <a href="<?php echo BASE_URL; ?>/admin/endImpersonation" class="btn btn-danger btn-sm">
                <i class="fas fa-sign-out-alt me-1"></i> Return to Admin Account
            </a>
        </div>
    </div>
    <?php endif; ?>

    <!-- Breadcrumb Navigation -->
    <?php include 'breadcrumb-nav.php'; ?>

    <!-- Main Content -->
    <main class="container py-4"><?php if (isset($flash_message)) : ?>
        <div class="alert alert-<?php echo $flash_message['type']; ?> alert-dismissible fade show" role="alert">
            <?php echo $flash_message['message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
