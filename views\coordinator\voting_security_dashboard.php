<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-0">
                <i class="fas fa-shield-alt me-2 text-danger"></i>
                Fan Voting Security Dashboard
            </h1>
            <p class="text-muted mb-0"><?php echo $data['show']->name; ?></p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group">
                <button class="btn btn-primary" onclick="openManualVoteModal()">
                    <i class="fas fa-plus me-2"></i>Add Manual Vote
                </button>
                <?php if (isset($data['flagged_count']) && $data['flagged_count'] > 0): ?>
                <button class="btn btn-warning" onclick="showFlaggedVotes()">
                    <i class="fas fa-flag me-2"></i>Flagged (<?php echo $data['flagged_count']; ?>)
                </button>
                <?php endif; ?>
                <a href="<?php echo URLROOT; ?>/coordinator/exportVotingSecurity/<?php echo $data['show']->id; ?>" class="btn btn-outline-primary">
                    <i class="fas fa-download me-2"></i>Export Report
                </a>
            </div>
        </div>
    </div>

    <?php flash('coordinator_message'); ?>

    <!-- Advanced Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-filter me-2"></i>Advanced Filters
                        <button class="btn btn-sm btn-outline-secondary float-end" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </h6>
                </div>
                <div class="collapse" id="filtersCollapse">
                    <div class="card-body">
                        <form method="GET" action="<?php echo BASE_URL; ?>/coordinator/votingSecurityDashboard/<?php echo $data['show']->id; ?>">
                            <div class="row g-3">
                                <!-- Search -->
                                <div class="col-md-4">
                                    <label for="search" class="form-label">Search</label>
                                    <input type="text" class="form-control" id="search" name="search"
                                           placeholder="Vehicle, IP, voter..."
                                           value="<?php echo htmlspecialchars($data['filters']['search'] ?? ''); ?>">
                                </div>

                                <!-- Status Filter -->
                                <div class="col-md-2">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="all" <?php echo ($data['filters']['status'] ?? 'all') === 'all' ? 'selected' : ''; ?>>All Status</option>
                                        <option value="approved" <?php echo ($data['filters']['status'] ?? '') === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                        <option value="flagged" <?php echo ($data['filters']['status'] ?? '') === 'flagged' ? 'selected' : ''; ?>>Flagged</option>
                                        <option value="pending" <?php echo ($data['filters']['status'] ?? '') === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    </select>
                                </div>

                                <!-- Risk Level Filter -->
                                <div class="col-md-2">
                                    <label for="risk" class="form-label">Risk Level</label>
                                    <select class="form-select" id="risk" name="risk">
                                        <option value="all" <?php echo ($data['filters']['risk_level'] ?? 'all') === 'all' ? 'selected' : ''; ?>>All Risk</option>
                                        <option value="high" <?php echo ($data['filters']['risk_level'] ?? '') === 'high' ? 'selected' : ''; ?>>High (70+)</option>
                                        <option value="medium" <?php echo ($data['filters']['risk_level'] ?? '') === 'medium' ? 'selected' : ''; ?>>Medium (40-69)</option>
                                        <option value="low" <?php echo ($data['filters']['risk_level'] ?? '') === 'low' ? 'selected' : ''; ?>>Low (0-39)</option>
                                    </select>
                                </div>

                                <!-- Results Per Page -->
                                <div class="col-md-2">
                                    <label for="limit" class="form-label">Per Page</label>
                                    <select class="form-select" id="limit" name="limit">
                                        <option value="10" <?php echo ($data['limit'] ?? 25) == 10 ? 'selected' : ''; ?>>10</option>
                                        <option value="25" <?php echo ($data['limit'] ?? 25) == 25 ? 'selected' : ''; ?>>25</option>
                                        <option value="50" <?php echo ($data['limit'] ?? 25) == 50 ? 'selected' : ''; ?>>50</option>
                                        <option value="100" <?php echo ($data['limit'] ?? 25) == 100 ? 'selected' : ''; ?>>100</option>
                                    </select>
                                </div>

                                <!-- Filter Actions -->
                                <div class="col-md-2 d-flex align-items-end">
                                    <div class="btn-group w-100">
                                        <button type="submit" class="btn btn-primary btn-sm">
                                            <i class="fas fa-search me-1"></i>Apply
                                        </button>
                                        <a href="<?php echo BASE_URL; ?>/coordinator/votingSecurityDashboard/<?php echo $data['show']->id; ?>" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-times me-1"></i>Clear
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Security Overview Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo $data['stats']['total_votes']; ?></h4>
                            <p class="card-text">Total Votes</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-vote-yea fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo $data['stats']['flagged_votes']; ?></h4>
                            <p class="card-text">Flagged Votes</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-flag fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo $data['stats']['high_risk_votes']; ?></h4>
                            <p class="card-text">High Risk</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo $data['stats']['approved_votes']; ?></h4>
                            <p class="card-text">Approved</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Security Alerts -->
    <?php if (!empty($data['alerts'])): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bell me-2"></i>
                        Security Alerts (<?php echo count($data['alerts']); ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php foreach ($data['alerts'] as $alert): ?>
                    <div class="alert alert-<?php echo $alert->severity === 'critical' ? 'danger' : ($alert->severity === 'warning' ? 'warning' : 'info'); ?> d-flex justify-content-between align-items-center">
                        <div>
                            <strong><?php echo $alert->title; ?></strong><br>
                            <small><?php echo $alert->message; ?></small><br>
                            <small class="text-muted"><?php echo date('M j, Y g:i A', strtotime($alert->created_at)); ?></small>
                        </div>
                        <div>
                            <a href="<?php echo URLROOT; ?>/coordinator/acknowledgeAlert/<?php echo $alert->id; ?>" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-check me-1"></i> Acknowledge
                            </a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Vote Management Tools -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tools me-2"></i>
                            Vote Management Tools
                        </h5>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#manualVoteModal">
                                <i class="fas fa-plus me-2"></i> Add Manual Vote
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="bulkFlagSelected()">
                                <i class="fas fa-flag me-2"></i> Flag Selected
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="bulkApproveSelected()">
                                <i class="fas fa-check me-2"></i> Approve Selected
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="bulkDeleteSelected()">
                                <i class="fas fa-trash me-2"></i> Delete Selected
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Bulk Actions and Pagination Info -->
                    <?php if (!empty($data['votes'])): ?>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="btn-group">
                            <button type="button" class="btn btn-success btn-sm" onclick="bulkApprove()">
                                <i class="fas fa-check me-1"></i>Approve Selected
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" onclick="bulkFlag()">
                                <i class="fas fa-flag me-1"></i>Flag Selected
                            </button>
                            <button type="button" class="btn btn-danger btn-sm" onclick="bulkDelete()">
                                <i class="fas fa-trash me-1"></i>Delete Selected
                            </button>
                        </div>

                        <!-- Pagination Info -->
                        <div class="text-muted">
                            <small>
                                Page <?php echo $data['current_page'] ?? 1; ?> of <?php echo $data['total_pages'] ?? 1; ?>
                                (<?php echo number_format($data['total_votes'] ?? 0); ?> total votes)
                            </small>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Optimized Votes Table -->
                    <?php if (!empty($data['votes'])): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                                    <th>Vehicle</th>
                                    <th>Voter Info</th>
                                    <th>Method</th>
                                    <th>Risk Score</th>
                                    <th>Status</th>
                                    <th>Location</th>
                                    <th>Time</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($data['votes'] as $vote): ?>
                                <tr class="<?php echo $vote->is_flagged ? 'table-warning' : ($vote->risk_score >= 70 ? 'table-danger' : ''); ?>">
                                    <td>
                                        <input type="checkbox" class="vote-checkbox" value="<?php echo $vote->id; ?>">
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($vote->vehicle_info ?? 'Unknown'); ?></strong><br>
                                        <small class="text-muted">ID: <?php echo $vote->registration_id; ?></small>
                                    </td>
                                    <td>
                                        <?php if ($vote->vote_method === 'facebook'): ?>
                                            <i class="fab fa-facebook text-primary me-1"></i>
                                            <small><?php echo htmlspecialchars($vote->fb_user_name ?? 'Facebook User'); ?></small>
                                        <?php elseif ($vote->manual_entered_by): ?>
                                            <i class="fas fa-user-edit text-info me-1"></i>
                                            <small>Manual by <?php echo htmlspecialchars($vote->entered_by_name ?? 'Staff'); ?></small>
                                        <?php else: ?>
                                            <i class="fas fa-globe text-secondary me-1"></i>
                                            <small><?php echo htmlspecialchars($vote->voter_ip); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $vote->vote_method === 'facebook' ? 'primary' : ($vote->manual_entered_by ? 'info' : 'secondary'); ?>">
                                            <?php echo ucfirst($vote->vote_method ?? 'ip'); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $vote->risk_score >= 90 ? 'danger' : ($vote->risk_score >= 70 ? 'warning' : 'success'); ?>">
                                            <?php echo $vote->risk_score; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($vote->is_flagged): ?>
                                            <span class="badge bg-warning text-dark">Flagged</span>
                                        <?php elseif ($vote->is_approved): ?>
                                            <span class="badge bg-success">Approved</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Pending</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($vote->distance_from_show !== null): ?>
                                            <span class="badge bg-<?php echo $vote->distance_from_show <= 0.5 ? 'success' : 'warning'; ?>">
                                                <?php echo number_format($vote->distance_from_show, 2); ?> mi
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small><?php echo date('M j, g:i A', strtotime($vote->created_at)); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <?php if ($vote->is_flagged): ?>
                                                <button type="button" class="btn btn-success btn-sm" onclick="approveVote(<?php echo $vote->id; ?>)" title="Approve">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="deleteVote(<?php echo $vote->id; ?>)" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination Controls -->
                    <?php if (isset($data['total_pages']) && $data['total_pages'] > 1): ?>
                    <div class="d-flex justify-content-center mt-3">
                        <nav aria-label="Vote pagination">
                            <ul class="pagination">
                                <?php if ($data['current_page'] > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $data['current_page'] - 1; ?>&<?php echo http_build_query(array_filter($data['filters'] ?? [])); ?>&limit=<?php echo $data['limit'] ?? 25; ?>">Previous</a>
                                    </li>
                                <?php endif; ?>

                                <?php
                                $start = max(1, $data['current_page'] - 5);
                                $end = min($data['total_pages'], $data['current_page'] + 5);
                                ?>

                                <?php if ($start > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1&<?php echo http_build_query(array_filter($data['filters'] ?? [])); ?>&limit=<?php echo $data['limit'] ?? 25; ?>">1</a>
                                    </li>
                                    <?php if ($start > 2): ?>
                                        <li class="page-item disabled"><span class="page-link">...</span></li>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php for ($i = $start; $i <= $end; $i++): ?>
                                    <li class="page-item <?php echo $i == $data['current_page'] ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query(array_filter($data['filters'] ?? [])); ?>&limit=<?php echo $data['limit'] ?? 25; ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($end < $data['total_pages']): ?>
                                    <?php if ($end < $data['total_pages'] - 1): ?>
                                        <li class="page-item disabled"><span class="page-link">...</span></li>
                                    <?php endif; ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $data['total_pages']; ?>&<?php echo http_build_query(array_filter($data['filters'] ?? [])); ?>&limit=<?php echo $data['limit'] ?? 25; ?>"><?php echo $data['total_pages']; ?></a>
                                    </li>
                                <?php endif; ?>

                                <?php if ($data['current_page'] < $data['total_pages']): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $data['current_page'] + 1; ?>&<?php echo http_build_query(array_filter($data['filters'] ?? [])); ?>&limit=<?php echo $data['limit'] ?? 25; ?>">Next</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                    <?php endif; ?>

                    <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-vote-yea fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No votes found</h5>
                        <p class="text-muted">No votes match the selected criteria.</p>
                        <a href="?show_id=<?php echo $data['show']->id; ?>" class="btn btn-outline-primary">
                            <i class="fas fa-times me-2"></i>Clear Filters
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Manual Vote Modal -->
<div class="modal fade" id="manualVoteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Manual Vote</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="<?php echo URLROOT; ?>/coordinator/addManualVote/<?php echo $data['show']->id; ?>">
                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="vehicle_search" class="form-label">Select Vehicle</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="vehicle_search" placeholder="Search vehicles..." readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="openVehicleSelector()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <input type="hidden" name="registration_id" id="selected_vehicle_id" required>
                        <small class="text-muted">Search by registration number, make, model, or year (<?php echo $data['vehicle_count'] ?? 0; ?> vehicles available)</small>
                    </div>
                    <div class="mb-3">
                        <label for="voter_name" class="form-label">Voter Name</label>
                        <input type="text" class="form-control" id="voter_name" name="voter_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="voter_contact" class="form-label">Voter Contact (Phone or Email)</label>
                        <input type="text" class="form-control" id="voter_contact" name="voter_contact" required>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Reason for manual entry, special circumstances, etc."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Vote</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Vehicle Selector Modal -->
<div class="modal fade" id="vehicleSelectorModal" tabindex="-1" aria-labelledby="vehicleSelectorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="vehicleSelectorModalLabel">Select Vehicle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="vehicleSearchInput" placeholder="Search vehicles by registration, make, model, or year...">
                </div>
                <div id="vehicleSearchResults" class="list-group" style="max-height: 400px; overflow-y: auto;">
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Manual vote modal functions
function openManualVoteModal() {
    const modal = new bootstrap.Modal(document.getElementById('manualVoteModal'));
    modal.show();
}

// Vehicle selector functionality
function openVehicleSelector() {
    const modal = new bootstrap.Modal(document.getElementById('vehicleSelectorModal'));
    modal.show();
    loadVehicles('');
}

// AJAX vehicle search
let vehicleSearchTimeout;
document.getElementById('vehicleSearchInput')?.addEventListener('input', function() {
    clearTimeout(vehicleSearchTimeout);
    const query = this.value;

    vehicleSearchTimeout = setTimeout(() => {
        loadVehicles(query);
    }, 300);
});

function loadVehicles(query) {
    const resultsContainer = document.getElementById('vehicleSearchResults');
    resultsContainer.innerHTML = '<div class="text-center py-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';

    // AJAX call to search vehicles
    fetch(`<?php echo BASE_URL; ?>/coordinator/searchVehicles/<?php echo $data['show']->id; ?>?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            let html = '';

            if (data.vehicles && data.vehicles.length > 0) {
                data.vehicles.forEach(vehicle => {
                    html += `<a href="#" class="list-group-item list-group-item-action" onclick="selectVehicle(${vehicle.id}, '${vehicle.vehicle_info}', '${vehicle.registration_number}')">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">${vehicle.vehicle_info}</h6>
                                        <small class="text-muted">Registration #${vehicle.registration_number}</small>
                                    </div>
                                    <small class="text-muted">${vehicle.vote_count || 0} votes</small>
                                </div>
                             </a>`;
                });
            } else {
                html += '<div class="text-center py-3 text-muted">No vehicles found</div>';
            }

            resultsContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading vehicles:', error);
            resultsContainer.innerHTML = '<div class="text-center py-3 text-danger">Error loading vehicles</div>';
        });
}

function selectVehicle(vehicleId, vehicleInfo, regNumber) {
    document.getElementById('vehicle_search').value = `${vehicleInfo} (Reg #${regNumber})`;
    document.getElementById('selected_vehicle_id').value = vehicleId;

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('vehicleSelectorModal'));
    modal.hide();
}

// Toggle select all checkboxes
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.vote-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// Get selected vote IDs
function getSelectedVotes() {
    const checkboxes = document.querySelectorAll('.vote-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// Bulk operations
function bulkFlag() {
    const selected = getSelectedVotes();
    if (selected.length === 0) {
        alert('Please select votes to flag.');
        return;
    }

    if (confirm(`Flag ${selected.length} selected votes?`)) {
        bulkAction('flag', selected);
    }
}

function bulkApprove() {
    const selected = getSelectedVotes();
    if (selected.length === 0) {
        alert('Please select votes to approve.');
        return;
    }

    if (confirm(`Approve ${selected.length} selected votes?`)) {
        bulkAction('approve', selected);
    }
}

function bulkDelete() {
    const selected = getSelectedVotes();
    if (selected.length === 0) {
        alert('Please select votes to delete.');
        return;
    }

    if (confirm(`Delete ${selected.length} selected votes? This action cannot be undone.`)) {
        bulkAction('delete', selected);
    }
}

// Show filtered views
function showFlaggedVotes() {
    window.location.href = '?status=flagged&<?php echo http_build_query(array_filter($data['filters'] ?? [])); ?>';
}

function showPendingAppeals() {
    // Navigate to appeals management (if implemented)
    alert('Appeals management coming soon!');
}

// Perform bulk action
function bulkAction(action, voteIds) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '<?php echo BASE_URL; ?>/coordinator/processBulkVoteAction/<?php echo $data['show']->id; ?>';

    form.innerHTML = `
        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
        <input type="hidden" name="bulk_action" value="${action}">
        ${voteIds.map(id => `<input type="hidden" name="vote_ids[]" value="${id}">`).join('')}
    `;

    document.body.appendChild(form);
    form.submit();
}

// Individual vote actions
function approveVote(voteId) {
    if (confirm('Approve this vote?')) {
        bulkAction('approve', [voteId]);
    }
}

function deleteVote(voteId) {
    if (confirm('Delete this vote? This action cannot be undone.')) {
        bulkAction('delete', [voteId]);
    }
}

// Auto-expand filters if any are set
document.addEventListener('DOMContentLoaded', function() {
    const hasFilters = <?php echo json_encode(array_filter($data['filters'] ?? [], function($v) { return !empty($v); })); ?>;
    if (Object.keys(hasFilters).length > 0) {
        const filtersCollapse = new bootstrap.Collapse(document.getElementById('filtersCollapse'), {
            show: true
        });
    }
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
